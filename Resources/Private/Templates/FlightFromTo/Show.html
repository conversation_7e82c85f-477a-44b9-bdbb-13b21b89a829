<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<!-- FlightFromTo Plugin Template -->
<f:if condition="{hasAnyList}">
    <f:then>
        <div class="active-offers__wrapper">
            <div class="active-offers__title"></div>
            <div class="active-offers__content">

        <div class="flight-from-to-container">
            <f:if condition="{hasFromList}">
                <div class="tp-card-container offer-card-2x2">
                    <div class="active-offers__title"><h2>Еднопосочни от [_destination_name_]</h2></div>
                    <div class="mdc-card tp-offer-card">
                    <f:for each="{fromList}" as="item">
                        <a class='tp'
                           data-type="flight-link"
                           data-title="До {item.name}"
                           data-endpoint="https://wizatour.fiestatravel.bg/wt/bg_BG/fieFlights/Search/hotels"
                           href="https://www.fiestatravel.bg/poleti/rezultati-poleti?flight-type=oneWay&period-type=flexible&adults=1&airports=[_destination_code_]&arrivalAirports={item.code}"></a>
                    </f:for>
                    </div>
                </div>
            </f:if>

            <f:if condition="{hasToList}">
                <div class="tp-card-container offer-card-2x2">
                    <div class="active-offers__title"><h2>Еднопосочни до [_destination_name_]</h2></div>
                    <div class="mdc-card tp-offer-card">
                    <f:for each="{toList}" as="item">
                        <a class='tp'
                           data-type="flight-link"
                           data-title="От {item.name}"
                           data-endpoint="https://wizatour.fiestatravel.bg/wt/bg_BG/fieFlights/Search/hotels"
                           href="https://www.fiestatravel.bg/poleti/rezultati-poleti?flight-type=oneWay&period-type=flexible&adults=1&airports=[_destination_code_]&arrivalAirports={item.code}"></a>
                    </f:for>
                    </div>
                </div>
            </f:if>
            </div>
        </div>
    </f:then>
</f:if>

</html>
