<?php
namespace Bgs\LandingPages\Controller;

use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Psr\Http\Message\ResponseInterface;

/**
 * Controller for FlightFromTo plugin
 */
class FlightFromToController extends ActionController
{

    /**
     * Show action - displays the from/to lists
     */
    public function showAction(): void
    {
        // DEBUG: Write to error log to see if controller is called
        error_log('DEBUG: FlightFromToController::showAction() called!');

        // Simple test - assign a test variable
        $this->view->assign('test', 'Controller is working!');

        // Get FlexForm configuration
        $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
        $contentObject = $this->configurationManager->getContentObject();
        $flexFormData = $flexFormService->convertFlexFormContentToArray($contentObject->data['pi_flexform'] ?? '');

        // DEBUG: Log FlexForm data
        error_log('DEBUG: FlexForm data: ' . print_r($flexFormData, true));

        // Get the raw from and to lists from FlexForm
        $fromListRaw = $flexFormData['from_list'] ?? '';
        $toListRaw = $flexFormData['to_list'] ?? '';

        // DEBUG: Log raw data
        error_log('DEBUG: fromListRaw: ' . $fromListRaw);
        error_log('DEBUG: toListRaw: ' . $toListRaw);

        // Parse the lists
        $fromList = $this->parseCodeNameList($fromListRaw);
        $toList = $this->parseCodeNameList($toListRaw);

        // Assign to view
        $this->view->assign('fromList', $fromList);
        $this->view->assign('toList', $toList);
        $this->view->assign('hasFromList', !empty($fromList));
        $this->view->assign('hasToList', !empty($toList));
        $this->view->assign('hasAnyList', !empty($fromList) || !empty($toList));

        error_log('DEBUG: Controller finished, assigned variables to view');
    }

    /**
     * Parse a code-name list from textarea input
     * Expected format: "CODE - Name" per line
     *
     * @param string $listText Raw textarea content
     * @return array Array of parsed items with 'code' and 'name' keys
     */
    protected function parseCodeNameList(string $listText): array
    {
        $items = [];
        
        if (empty(trim($listText))) {
            return $items;
        }

        // Split by lines and process each line
        $lines = explode("\n", $listText);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Look for the pattern "CODE - Name"
            if (strpos($line, ' - ') !== false) {
                $parts = explode(' - ', $line, 2);
                $code = trim($parts[0]);
                $name = trim($parts[1]);
                
                if (!empty($code) && !empty($name)) {
                    $items[] = [
                        'code' => $code,
                        'name' => $name,
                        'display' => $code . ' - ' . $name
                    ];
                }
            } else {
                // If no dash separator found, treat the whole line as code
                $items[] = [
                    'code' => $line,
                    'name' => '',
                    'display' => $line
                ];
            }
        }

        return $items;
    }
}
